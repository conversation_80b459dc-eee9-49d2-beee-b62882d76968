/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.client.usercore.merchantadmin;

import com.frt.generalgw.config.FeignConfig;
import com.frt.generalgw.domain.param.common.PageParam;
import com.frt.generalgw.domain.param.merchantadmin.storemanager.StoreInfoAddParam;
import com.frt.generalgw.domain.param.merchantadmin.storemanager.StoreInfoQueryParam;
import com.frt.generalgw.domain.param.merchantadmin.storemanager.StoreInfoUpdateParam;
import com.frt.generalgw.domain.param.merchantadmin.storemanager.StoreListQueryParam;
import com.frt.generalgw.domain.result.common.PageResult;
import com.frt.generalgw.domain.result.merchantadmin.storemanager.StoreInfoAddResult;
import com.frt.generalgw.domain.result.merchantadmin.storemanager.StoreInfoQueryResult;
import com.frt.generalgw.domain.result.merchantadmin.storemanager.StoreInfoUpdateResult;
import com.frt.generalgw.domain.result.merchantadmin.storemanager.StoreListQueryResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        contextId = "merchant-admin-store-manager-client",
        // nacos 服务 id
        value = "frt-usercore-dev",
        configuration = {FeignConfig.class}
)
public interface StoreManagerClient {

    /**
     * 查询门店列表
     * @param param
     * @return
     */
    @PostMapping("/api/store/query/list")
    PageResult<StoreListQueryResult> queryStoreList(@RequestBody PageParam<StoreListQueryParam> param);

    /**
     * 添加门店信息
     * @param param
     * @return
     */
    @PostMapping("/api/store/add/info")
    StoreInfoAddResult addStoreInfo(@RequestBody StoreInfoAddParam param);

    /**
     * 修改门店信息
     * @param param
     * @return
     */
    @PostMapping("/api/store/update/info")
    StoreInfoUpdateResult updateStoreInfo(@RequestBody StoreInfoUpdateParam param);

    /**
     * 查询门店信息
     * @param param
     * @return
     */
    @PostMapping("/api/store/query/info")
    StoreInfoQueryResult queryStoreInfo(@RequestBody StoreInfoQueryParam param);
}
/**
 * <AUTHOR>
 * @date 2025/8/27 14:45
 * @version 1.0 OperationAdminController
 */
package com.frt.generalgw.controller.operationadmin;

import com.frt.generalgw.domain.param.operationadmin.forgotpassword.CheckSmsCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.CheckVerifyCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.GetVerifyCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.SendSmsParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.UpdatePasswordParam;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.CheckVerifyCodeResult;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.GetVerifyCodeResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 运营后台/OperationAdminAuthController
 *
 * <AUTHOR>
 * @version OperationAdminController.java, v 0.1 2025-08-27 14:45 tuyuwei
 */
@RestController
@RequestMapping("/operation/web")
public class OperationAdminAuthController {

    /**
     * 获取图形验证码
     *
     * @param param 请求参数
     * @return 验证码图片URL
     */
    @PostMapping("/get-verify-code")
    public GetVerifyCodeResult getVerifyCode(@Validated @RequestBody GetVerifyCodeParam param) {
        // TODO: 实现获取图形验证码逻辑
        return new GetVerifyCodeResult();
    }

    /**
     * 校验图文验证码
     *
     * @param param 请求参数
     * @return 校验结果
     */
    @PostMapping("/check-verify-code")
    public CheckVerifyCodeResult checkVerifyCode(@Validated @RequestBody CheckVerifyCodeParam param) {
        // TODO: 实现校验图文验证码逻辑
        return new CheckVerifyCodeResult();
    }

    /**
     * 发送短信
     *
     * @param param 请求参数
     */
    @PostMapping("/sen-sms")
    public void sendSms(@Validated @RequestBody SendSmsParam param) {
        // TODO: 实现发送短信逻辑
    }

    /**
     * 校验验证码
     *
     * @param param 请求参数
     */
    @PostMapping("/check-sms-code")
    public void checkSmsCode(@Validated @RequestBody CheckSmsCodeParam param) {
        // TODO: 实现校验短信验证码逻辑
    }

    /**
     * 修改密码
     *
     * @param param 请求参数
     */
    @PostMapping("/update-password")
    public void updatePassword(@Validated @RequestBody UpdatePasswordParam param) {
        // TODO: 实现修改密码逻辑
    }
}
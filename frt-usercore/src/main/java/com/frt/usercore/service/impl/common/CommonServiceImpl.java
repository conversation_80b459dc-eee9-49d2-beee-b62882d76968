package com.frt.usercore.service.impl.common;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.beust.jcommander.internal.Lists;
import com.frt.usercore.common.constants.CommonConstant;
import com.frt.usercore.dao.entity.GaodeCodeDO;
import com.frt.usercore.dao.entity.UnityCategoryDO;
import com.frt.usercore.dao.repository.GaodeCodeDAO;
import com.frt.usercore.dao.repository.UnityCategoryDAO;
import com.frt.usercore.manager.domain.mapper.CommonMapper;
import com.frt.usercore.manager.domain.param.common.CommonAddressCodeListQueryParam;
import com.frt.usercore.manager.domain.param.common.CommonUnityCategoryListQueryParam;
import com.frt.usercore.manager.domain.result.common.CommonAddressCodeListQueryResult;
import com.frt.usercore.manager.domain.result.common.CommonUnityCategoryListQueryResult;
import com.frt.usercore.service.common.CommonService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CommonServiceImpl implements CommonService {

    @Autowired
    private GaodeCodeDAO gaodeCodeDAO;
    @Autowired
    private UnityCategoryDAO unityCategoryDAO;

    @Autowired
    private CommonMapper commonMapper;

    @Override
    public CommonAddressCodeListQueryResult queryAddressCodeList(CommonAddressCodeListQueryParam param) {

        List<GaodeCodeDO> gaodeCodeDOList = gaodeCodeDAO.lambdaQuery()
                .eq(StrUtil.isNotBlank(param.getProvinceCode()), GaodeCodeDO::getProvince, param.getProvinceCode())
                .eq(StrUtil.isNotBlank(param.getCityCode()), GaodeCodeDO::getCode, param.getCityCode())
                .eq(GaodeCodeDO::getLevel, this.getLevel(param))
                .orderByDesc(GaodeCodeDO::getSort)
                .list();
        List<CommonAddressCodeListQueryResult.CommonAddressCodeInfoQueryResult> list = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(gaodeCodeDOList)) {
            list = gaodeCodeDOList.stream().map(item ->
                            CommonAddressCodeListQueryResult.CommonAddressCodeInfoQueryResult.init(item.getCode(), item.getName()))
                    .collect(Collectors.toList());
        }

        return CommonAddressCodeListQueryResult.init(
                getGaoDeNameByCode(param.getProvinceCode()).get(CommonConstant.NAME_STR),
                getGaoDeNameByCode(param.getProvinceCode()).get(CommonConstant.CODE_STR),
                getGaoDeNameByCode(param.getCityCode()).get(CommonConstant.NAME_STR),
                getGaoDeNameByCode(param.getCityCode()).get(CommonConstant.CODE_STR),
                list);
    }

    @Override
    public CommonUnityCategoryListQueryResult queryUnityCategoryList(CommonUnityCategoryListQueryParam param) {

        // 查询1级类目
        List<UnityCategoryDO> firstList = unityCategoryDAO.lambdaQuery()
                .eq(UnityCategoryDO::getLevel, 1)
                .orderByDesc(UnityCategoryDO::getSort)
                .list();

        // 查询2级类目
        List<UnityCategoryDO> secondList = unityCategoryDAO.lambdaQuery()
                .eq(UnityCategoryDO::getLevel, 2)
                .list();
        Map<Integer, List<UnityCategoryDO>> secondListMap = secondList.stream()
                .collect(Collectors.groupingBy(UnityCategoryDO::getParentId));

        // 封装结果
        List<CommonUnityCategoryListQueryResult.CommonUnityCategoryInfoQueryResult> resultList = Lists.newArrayList();
        for (UnityCategoryDO firstModel : firstList) {
            CommonUnityCategoryListQueryResult.CommonUnityCategoryInfoQueryResult firstInfo = commonMapper.changeToUnityCategoryInfoQueryResult(firstModel);
            List<UnityCategoryDO> secondInfoDOList = secondListMap.get(firstModel.getId());
            List<CommonUnityCategoryListQueryResult.CommonUnityCategoryInfoQueryResult> secondInfoList = commonMapper.changeToUnityCategoryInfoListQueryResult(secondInfoDOList);
            firstInfo.setChildren(CollectionUtil.isNotEmpty(secondInfoList) ? secondInfoList : Lists.newArrayList());
        }

        CommonUnityCategoryListQueryResult result = new CommonUnityCategoryListQueryResult();
        result.setList(resultList);
        return result;
    }

    /**
     * 根据code获取信息
     */
    private Map<String, String> getGaoDeNameByCode(String code) {
        Map<String, String> map = Maps.newHashMap();
        map.put(CommonConstant.CODE_STR, StrUtil.EMPTY);
        map.put(CommonConstant.NAME_STR, StrUtil.EMPTY);
        if (StringUtils.isBlank(code)) {
            return map;
        }
        GaodeCodeDO gaodeCodeDO = new GaodeCodeDO();
        if (!StrUtil.equalsIgnoreCase(CommonConstant.ZERO_STR, code)) {
            gaodeCodeDO = gaodeCodeDAO.lambdaQuery()
                    .eq(GaodeCodeDO::getCode, code)
                    .one();
        }
        map.put(CommonConstant.NAME_STR, ObjectUtil.isNotNull(gaodeCodeDO) && StringUtils.isNotBlank(gaodeCodeDO.getName()) ? gaodeCodeDO.getName() : StrUtil.EMPTY);
        map.put(CommonConstant.CODE_STR, ObjectUtil.isNotNull(gaodeCodeDO) && StringUtils.isNotBlank(gaodeCodeDO.getCode()) ? gaodeCodeDO.getCode() : StrUtil.EMPTY);
        return map;
    }


    /**
     * 获取等级
     *
     * @param param
     * @return
     */
    private Integer getLevel(CommonAddressCodeListQueryParam param) {
        int level = 0;
        if (ObjectUtil.isNotNull(param.getProvinceCode())) {
            level = 1;
        }
        if (ObjectUtil.isNotNull(param.getCityCode())) {
            level = 2;
        }
        return level;
    }

}

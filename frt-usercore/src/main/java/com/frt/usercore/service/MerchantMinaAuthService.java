package com.frt.usercore.service;

import com.frt.usercore.manager.domain.param.merchantmina.auth.*;
import com.frt.usercore.manager.domain.result.merchantmina.auth.MerchantMinaLoginResult;
import com.frt.usercore.manager.domain.result.merchantmina.auth.MerchantMinaResourceResult;
import com.frt.usercore.manager.domain.result.merchantmina.auth.MerchantMinaSearchPhoneResult;

/**
 * 商户小程序权限服务接口
 */
public interface MerchantMinaAuthService {

    /**
     * 登录页资源获取
     *
     * @param param 请求参数
     * @return 资源信息
     */
    MerchantMinaResourceResult searchResource(MerchantMinaResourceParam param);

    /**
     * 发送验证码
     *
     * @param param 请求参数
     */
    void sendCode(MerchantMinaSendCodeParam param);

    /**
     * 账号登录
     *
     * @param param 登录参数
     * @return 登录结果
     */
    MerchantMinaLoginResult login(MerchantMinaLoginParam param);

    /**
     * 通过账号查询加密手机号
     *
     * @param param 查询参数
     * @return 手机号信息
     */
    MerchantMinaSearchPhoneResult searchPhone(MerchantMinaSearchPhoneParam param);

    /**
     * 修改密码验证码校验
     *
     * @param param 验证参数
     */
    void checkCode(MerchantMinaCheckCodeParam param);

    /**
     * 设置新密码
     *
     * @param param 修改密码参数
     */
    void changePassword(MerchantMinaChangePasswordParam param);

    /**
     * 账号登出
     */
    void logout();
}
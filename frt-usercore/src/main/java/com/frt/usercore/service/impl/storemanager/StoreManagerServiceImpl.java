package com.frt.usercore.service.impl.storemanager;

import com.frt.usercore.dao.repository.MerchantStoreInfoDAO;
import com.frt.usercore.manager.domain.mapper.StoreManagerMapper;
import com.frt.usercore.manager.domain.param.PageParam;
import com.frt.usercore.manager.domain.param.storemanager.StoreInfoAddParam;
import com.frt.usercore.manager.domain.param.storemanager.StoreInfoQueryParam;
import com.frt.usercore.manager.domain.param.storemanager.StoreInfoUpdateParam;
import com.frt.usercore.manager.domain.param.storemanager.StoreListQueryParam;
import com.frt.usercore.manager.domain.result.PageResult;
import com.frt.usercore.manager.domain.result.storemanager.StoreInfoAddResult;
import com.frt.usercore.manager.domain.result.storemanager.StoreInfoQueryResult;
import com.frt.usercore.manager.domain.result.storemanager.StoreInfoUpdateResult;
import com.frt.usercore.manager.domain.result.storemanager.StoreListQueryResult;
import com.frt.usercore.service.storemanager.StoreManagerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class StoreManagerServiceImpl implements StoreManagerService {

    @Autowired
    private MerchantStoreInfoDAO merchantStoreInfoDAO;

    @Autowired
    private StoreManagerMapper storeManagerMapper;

    @Override
    public PageResult<StoreListQueryResult> queryStoreList(PageParam<StoreListQueryParam> param) {
        return null;
    }

    @Override
    public StoreInfoAddResult addStoreInfo(StoreInfoAddParam param) {
        return null;
    }

    @Override
    public StoreInfoUpdateResult updateStoreInfo(StoreInfoUpdateParam param) {
        return null;
    }

    @Override
    public StoreInfoQueryResult queryStoreInfo(StoreInfoQueryParam param) {
        return null;
    }
}

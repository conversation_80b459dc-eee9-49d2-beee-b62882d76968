package com.frt.usercore.manager.domain.param.rolemanager;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 角色添加参数
 *
 * <AUTHOR>
 * @version RoleAddParam.java, v 0.1 2025-08-27 14:52 zhangling
 */
@Data
public class RoleAddParam implements Serializable {

    private static final long serialVersionUID = -4062190979663205032L;
    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色类型 0-超级管理员 1-普通角色
     */
    private Integer roleType;

    /**
     * 终端类型 1-商户端 2-运营端
     */
    private Integer terminalType;

    /**
     * 权限列表
     */
    private List<String> permissionValueList;
}
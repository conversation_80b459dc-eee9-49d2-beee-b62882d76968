package com.frt.usercore.manager.domain.result.merchantmina.auth;

import lombok.Getter;
import lombok.Setter;
import java.util.List;

/**
 * 登录结果
 */
@Getter
@Setter
public class MerchantMinaLoginResult {
    private Integer isResetPwd;
    private String token;
    private List<String> menuList;
    private List<String> funcList;
    private UserInfo userInfo;
    private TenantInfo tenantInfo;
    private List<ProtocolInfo> protocolList;

    @Getter
    @Setter
    public static class UserInfo {
        private String accountId;
        private String account;
        private String isAdmin;
        private String name;
        private String phone;
    }

    @Getter
    @Setter
    public static class TenantInfo {
        private String tenantId;
        private String tenantName;
        private String phone;
    }

    @Getter
    @Setter
    public static class ProtocolInfo {
        private String protocolId;
        private String protocolName;
        private String protocolType;
    }
}
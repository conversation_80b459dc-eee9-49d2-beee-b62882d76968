package com.frt.usercore.manager.domain.result.common;

import lombok.Data;

import java.util.List;

@Data
public class CommonAddressCodeListQueryResult {
    /**
     * 省
     */
    public String provinceName;
    /**
     * 省code
     */
    public String provinceCode;
    /**
     * 市
     */
    public String cityName;
    /**
     * 市code
     */
    public String cityCode;
    /**
     * 列表
     */
    public List<CommonAddressCodeInfoQueryResult> list;


    @Data
    public static class CommonAddressCodeInfoQueryResult {
        /**
         * 地区code
         */
        public String code;
        /**
         * 地区name
         */
        public String name;


        /**
         * 数据初始化
         */
        public static CommonAddressCodeInfoQueryResult init(String code, String name) {
            CommonAddressCodeInfoQueryResult model = new CommonAddressCodeInfoQueryResult();
            model.setCode(code);
            model.setName(name);
            return model;
        }
    }

    /**
     * 数据初始化
     */
    public static CommonAddressCodeListQueryResult init(String province,
                                                        String provinceCode,
                                                        String city,
                                                        String cityCode,
                                                        List<CommonAddressCodeInfoQueryResult> list) {
        CommonAddressCodeListQueryResult model = new CommonAddressCodeListQueryResult();
        model.setProvinceName(province);
        model.setProvinceCode(provinceCode);
        model.setCityName(city);
        model.setCityCode(cityCode);
        model.setList(list);
        return model;
    }
}

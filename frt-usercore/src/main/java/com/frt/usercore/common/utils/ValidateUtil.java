package com.frt.usercore.common.utils;

import cn.hutool.core.util.StrUtil;
import com.frt.usercore.common.constants.base.BaseConstants;
import com.frt.usercore.common.enums.exception.VerificationErrorEnum;
import com.frt.usercore.common.exception.InternalException;
import com.frt.usercore.manager.domain.result.base.ValidateResult;
import com.google.common.base.CaseFormat;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Objects;
import java.util.Set;

import static com.google.common.collect.Iterables.getFirst;

/**
 * <AUTHOR>
 * @version ValidateUtil.java, v 0.1 2025-08-27 15:01 wangyi
 */
@Slf4j
public class ValidateUtil {

    private static final Validator VALIDATOR = Validation.buildDefaultValidatorFactory().getValidator();

    /**
     * 对象参数通用校验
     *
     * @param obj
     */
    public static void validate(Object obj) {
        if (Objects.isNull(obj)) {
            throw VerificationErrorEnum.PARAMETER_IS_NULL.exception();
        }
        ValidateResult validateResult = baseValidate(obj);
        if (!validateResult.isResult()) {
            LogUtil.error(log, "validate >> 参数校验错误 >> paramName={},msg={}", validateResult.getParamName(), validateResult.getMsg());
            String msg = validateResult.getParamName() + validateResult.getMsg();
            if (StrUtil.containsIgnoreCase(validateResult.getMsg(), validateResult.getParamName())) {
                msg = validateResult.getMsg();
            }
            throw validateMsg(msg);
        }
    }

    /**
     * 通用校验错误异常
     *
     * @param msg
     * @return
     */
    public static InternalException validateMsg(String msg) {
        return VerificationErrorEnum.PARAMETER_VALIDATION_FAILED.exception(StrUtil.format(BaseConstants.VALIDATE_ERROR_MSG_TEMPLATE, msg));
    }

    /**
     * 验证参数
     *
     * @param object
     * @param <T>
     * @return
     */
    public static <T> ValidateResult baseValidate(T object) {
        ValidateResult validateResult = new ValidateResult();
        validateResult.setResult(true);
        //执行验证
        Set<ConstraintViolation<T>> constraintViolations = VALIDATOR.validate(object);
        //如果有验证信息，则将第一个取出来包装成异常返回
        ConstraintViolation<T> constraintViolation = getFirst(constraintViolations, null);
        if (constraintViolation != null) {
            validateResult.setResult(false);
            validateResult.setParamName(CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, constraintViolation.getPropertyPath().toString()));
            validateResult.setMsg(constraintViolation.getMessage());
        }
        return validateResult;
    }
}